import os
import boto3
from datetime import datetime
from botocore.exceptions import ClientError

dynamodb = boto3.resource('dynamodb')
TABLE_NAME = os.environ.get('DYNAMODB_TABLE')
table = dynamodb.Table(TABLE_NAME) if TABLE_NAME else None


def save_file_metadata(file_data):
    if not table:
        raise Exception("DynamoDB table not configured")

    try:
        timestamp = datetime.utcnow().isoformat()

        item = {
            'PK': f"USER#{file_data['userId']}",
            'SK': f"FILE#{file_data['fileId']}",
            'GSI1PK': f"FILE#{file_data['fileId']}",
            'GSI1SK': timestamp,
            'fileId': file_data['fileId'],
            'fileName': file_data['fileName'],
            'fileSize': file_data['fileSize'],
            's3Key': file_data['s3Key'],
            'contentType': file_data.get('contentType', 'application/octet-stream'),
            'status': file_data.get('status', 'pending'),
            'uploadedAt': timestamp,
            'userId': file_data['userId'],
            'metadata': file_data.get('metadata', {})
        }

        table.put_item(Item=item)

        return item

    except ClientError as e:
        print(f"Error saving file metadata: {e}")
        raise e


def get_file_metadata(file_id):
    if not table:
        raise Exception("DynamoDB table not configured")

    try:
        response = table.query(
            IndexName='GSI1',
            KeyConditionExpression='GSI1PK = :pk',
            ExpressionAttributeValues={
                ':pk': f"FILE#{file_id}"
            },
            Limit=1
        )

        if response['Items']:
            return response['Items'][0]

        return None

    except ClientError as e:
        print(f"Error getting file metadata: {e}")
        raise e


def update_file_status(file_id, status, additional_data=None):
    if not table:
        raise Exception("DynamoDB table not configured")

    try:
        file_metadata = get_file_metadata(file_id)
        if not file_metadata:
            raise Exception("File not found")

        update_expression = "SET #status = :status, #updatedAt = :updatedAt"
        expression_values = {
            ':status': status,
            ':updatedAt': datetime.utcnow().isoformat()
        }
        expression_names = {
            '#status': 'status',
            '#updatedAt': 'updatedAt'
        }

        if additional_data:
            for key, value in additional_data.items():
                update_expression += f", #{key} = :{key}"
                expression_values[f":{key}"] = value
                expression_names[f"#{key}"] = key

        table.update_item(
            Key={
                'PK': file_metadata['PK'],
                'SK': file_metadata['SK']
            },
            UpdateExpression=update_expression,
            ExpressionAttributeValues=expression_values,
            ExpressionAttributeNames=expression_names
        )

        return True

    except ClientError as e:
        print(f"Error updating file status: {e}")
        raise e


def list_user_files(user_id, limit=50, last_evaluated_key=None):
    if not table:
        raise Exception("DynamoDB table not configured")

    try:
        query_params = {
            'KeyConditionExpression': 'PK = :pk AND begins_with(SK, :sk)',
            'ExpressionAttributeValues': {
                ':pk': f"USER#{user_id}",
                ':sk': "FILE#"
            },
            'Limit': limit,
            'ScanIndexForward': False
        }

        if last_evaluated_key:
            query_params['ExclusiveStartKey'] = last_evaluated_key

        response = table.query(**query_params)

        return {
            'items': response['Items'],
            'lastEvaluatedKey': response.get('LastEvaluatedKey')
        }

    except ClientError as e:
        print(f"Error listing user files: {e}")
        raise e


def save_analysis_metadata(analysis_data):
    if not table:
        raise Exception("DynamoDB table not configured")

    try:
        timestamp = datetime.utcnow().isoformat()

        item = {
            'PK': f"USER#{analysis_data['userId']}",
            'SK': f"ANALYSIS#{analysis_data['analysisId']}",
            'GSI1PK': f"ANALYSIS#{analysis_data['analysisId']}",
            'GSI1SK': timestamp,
            'analysisId': analysis_data['analysisId'],
            'fileId': analysis_data['fileId'],
            'status': analysis_data.get('status', 'pending'),
            'settings': analysis_data['settings'],
            'createdAt': timestamp,
            'userId': analysis_data['userId'],
            'stepFunctionExecutionArn': analysis_data.get('executionArn')
        }

        table.put_item(Item=item)

        return item

    except ClientError as e:
        print(f"Error saving analysis metadata: {e}")
        raise e


def get_analysis_metadata(analysis_id):
    if not table:
        raise Exception("DynamoDB table not configured")

    try:
        response = table.query(
            IndexName='GSI1',
            KeyConditionExpression='GSI1PK = :pk',
            ExpressionAttributeValues={
                ':pk': f"ANALYSIS#{analysis_id}"
            },
            Limit=1
        )

        if response['Items']:
            return response['Items'][0]

        return None

    except ClientError as e:
        print(f"Error getting analysis metadata: {e}")
        raise e
