import pytest
import boto3
import os
from moto import mock_s3, mock_dynamodb
from dotenv import load_dotenv

load_dotenv('.env.test')


@pytest.fixture(scope='function')
def aws_credentials():
    os.environ['AWS_ACCESS_KEY_ID'] = 'test'
    os.environ['AWS_SECRET_ACCESS_KEY'] = 'test'
    os.environ['AWS_SECURITY_TOKEN'] = 'test'
    os.environ['AWS_SESSION_TOKEN'] = 'test'
    os.environ['AWS_DEFAULT_REGION'] = 'us-east-1'


@pytest.fixture(scope='function')
def s3_client(aws_credentials):
    with mock_s3():
        yield boto3.client('s3', region_name='us-east-1')


@pytest.fixture(scope='function')
def s3_bucket(s3_client):
    bucket_name = 'test-biormika-files'
    s3_client.create_bucket(Bucket=bucket_name)
    return bucket_name


@pytest.fixture(scope='function')
def dynamodb_resource(aws_credentials):
    with mock_dynamodb():
        yield boto3.resource('dynamodb', region_name='us-east-1')


@pytest.fixture(scope='function')
def dynamodb_table(dynamodb_resource):
    table_name = 'test-biormika-table'
    table = dynamodb_resource.create_table(
        TableName=table_name,
        KeySchema=[
            {'AttributeName': 'PK', 'KeyType': 'HASH'},
            {'AttributeName': 'SK', 'KeyType': 'RANGE'}
        ],
        AttributeDefinitions=[
            {'AttributeName': 'PK', 'AttributeType': 'S'},
            {'AttributeName': 'SK', 'AttributeType': 'S'}
        ],
        BillingMode='PAY_PER_REQUEST'
    )
    table.wait_until_exists()
    return table


@pytest.fixture
def sample_edf_data():
    import numpy as np

    sampling_rate = 256
    duration = 10
    num_channels = 4
    num_samples = sampling_rate * duration

    data = np.random.randn(num_channels, num_samples)

    for ch in range(num_channels):
        t = np.linspace(0, duration, num_samples)
        data[ch] += 0.5 * np.sin(2 * np.pi * 100 * t)

        if ch == 0:
            hfo_start = int(3 * sampling_rate)
            hfo_end = int(3.05 * sampling_rate)
            data[ch, hfo_start:hfo_end] += 2 * np.sin(2 * np.pi * 200 * t[hfo_start:hfo_end])

    return {
        'data': data,
        'sampling_rate': sampling_rate,
        'duration': duration,
        'num_channels': num_channels
    }


@pytest.fixture
def analysis_params():
    return {
        'lowCutoffFilter': 80,
        'highCutoffFilter': 250,
        'thresholdSettings': {
            'amplitude1': 5,
            'amplitude2': 3,
            'duration': 10,
            'peaks1': 6,
            'peaks2': 3
        },
        'synchronizationSettings': {
            'temporalSync': 10,
            'spatialSync': 0
        },
        'segmentSelectionSettings': {
            'startTime': 0,
            'endTime': -1
        }
    }
