import json
import os
import uuid
import boto3
from auth.decorators import auth_required
from storage.metadata import get_file_metadata, save_analysis_metadata, get_analysis_metadata

stepfunctions = boto3.client('stepfunctions')
STATE_MACHINE_ARN = os.environ.get('STATE_MACHINE_ARN')


def create_response(status_code, body):
    return {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': True,
        },
        'body': json.dumps(body)
    }


@auth_required
def start_analysis(event, context):
    try:
        body = json.loads(event['body'])
        file_id = body.get('fileId')
        settings = body.get('settings', {})

        if not file_id:
            return create_response(400, {
                'success': False,
                'message': 'File ID is required',
                'statusCode': 400
            })

        user = event.get('user', {})
        user_id = user.get('id')

        file_metadata = get_file_metadata(file_id)

        if not file_metadata:
            return create_response(404, {
                'success': False,
                'message': 'File not found',
                'statusCode': 404
            })

        if file_metadata['userId'] != user_id:
            return create_response(403, {
                'success': False,
                'message': 'Unauthorized access to file',
                'statusCode': 403
            })

        if file_metadata['status'] != 'valid':
            return create_response(400, {
                'success': False,
                'message': 'File must be validated before analysis',
                'statusCode': 400
            })

        analysis_id = str(uuid.uuid4())

        step_function_input = {
            'analysisId': analysis_id,
            'fileId': file_id,
            's3Key': file_metadata['s3Key'],
            'userId': user_id,
            'settings': settings,
            'fileInfo': file_metadata.get('fileInfo', {})
        }

        try:
            execution_response = stepfunctions.start_execution(
                stateMachineArn=STATE_MACHINE_ARN,
                name=f"analysis-{analysis_id}",
                input=json.dumps(step_function_input)
            )

            save_analysis_metadata({
                'analysisId': analysis_id,
                'fileId': file_id,
                'userId': user_id,
                'settings': settings,
                'status': 'running',
                'executionArn': execution_response['executionArn']
            })

            return create_response(200, {
                'success': True,
                'data': {
                    'analysisId': analysis_id,
                    'status': 'started',
                    'executionArn': execution_response['executionArn']
                },
                'message': 'Analysis started successfully',
                'statusCode': 200
            })

        except Exception as e:
            print(f"Error starting Step Function: {e}")
            return create_response(500, {
                'success': False,
                'message': 'Failed to start analysis',
                'statusCode': 500
            })

    except json.JSONDecodeError:
        return create_response(400, {
            'success': False,
            'message': 'Invalid request body',
            'statusCode': 400
        })
    except Exception as e:
        print(f"Start analysis error: {str(e)}")
        return create_response(500, {
            'success': False,
            'message': 'Internal server error',
            'statusCode': 500
        })


@auth_required
def get_status(event, context):
    try:
        analysis_id = event['pathParameters'].get('analysisId')

        if not analysis_id:
            return create_response(400, {
                'success': False,
                'message': 'Analysis ID is required',
                'statusCode': 400
            })

        user = event.get('user', {})
        user_id = user.get('id')

        analysis_metadata = get_analysis_metadata(analysis_id)

        if not analysis_metadata:
            return create_response(404, {
                'success': False,
                'message': 'Analysis not found',
                'statusCode': 404
            })

        if analysis_metadata['userId'] != user_id:
            return create_response(403, {
                'success': False,
                'message': 'Unauthorized access to analysis',
                'statusCode': 403
            })

        execution_arn = analysis_metadata.get('stepFunctionExecutionArn')

        if execution_arn:
            try:
                execution_response = stepfunctions.describe_execution(
                    executionArn=execution_arn
                )

                status = execution_response['status']

                if status == 'SUCCEEDED':
                    analysis_status = 'completed'
                elif status == 'FAILED':
                    analysis_status = 'failed'
                elif status == 'RUNNING':
                    analysis_status = 'running'
                else:
                    analysis_status = 'unknown'

                progress_data = {}
                if execution_response.get('output'):
                    try:
                        output_data = json.loads(execution_response['output'])
                        progress_data = output_data.get('progress', {})
                    except BaseException:
                        pass

                return create_response(200, {
                    'success': True,
                    'data': {
                        'analysisId': analysis_id,
                        'status': analysis_status,
                        'progress': progress_data,
                        'createdAt': analysis_metadata.get('createdAt')
                    },
                    'message': 'Status retrieved successfully',
                    'statusCode': 200
                })

            except Exception as e:
                print(f"Error getting execution status: {e}")
                return create_response(200, {
                    'success': True,
                    'data': {
                        'analysisId': analysis_id,
                        'status': analysis_metadata.get('status', 'unknown')
                    },
                    'message': 'Status retrieved from metadata',
                    'statusCode': 200
                })
        else:
            return create_response(200, {
                'success': True,
                'data': {
                    'analysisId': analysis_id,
                    'status': analysis_metadata.get('status', 'pending')
                },
                'message': 'Status retrieved successfully',
                'statusCode': 200
            })

    except Exception as e:
        print(f"Get status error: {str(e)}")
        return create_response(500, {
            'success': False,
            'message': 'Internal server error',
            'statusCode': 500
        })


@auth_required
def get_results(event, context):
    try:
        analysis_id = event['pathParameters'].get('analysisId')

        if not analysis_id:
            return create_response(400, {
                'success': False,
                'message': 'Analysis ID is required',
                'statusCode': 400
            })

        user = event.get('user', {})
        user_id = user.get('id')

        analysis_metadata = get_analysis_metadata(analysis_id)

        if not analysis_metadata:
            return create_response(404, {
                'success': False,
                'message': 'Analysis not found',
                'statusCode': 404
            })

        if analysis_metadata['userId'] != user_id:
            return create_response(403, {
                'success': False,
                'message': 'Unauthorized access to analysis',
                'statusCode': 403
            })

        results_key = f"users/{user_id}/results/{analysis_id}/results.json"

        try:
            s3 = boto3.client('s3')
            S3_BUCKET = os.environ.get('S3_BUCKET')

            response = s3.get_object(Bucket=S3_BUCKET, Key=results_key)
            results_data = json.loads(response['Body'].read())

            report_key = f"users/{user_id}/results/{analysis_id}/report.pdf"
            report_url = s3.generate_presigned_url(
                'get_object',
                Params={'Bucket': S3_BUCKET, 'Key': report_key},
                ExpiresIn=3600
            )

            visualizations = []
            for i in range(results_data.get('num_visualizations', 0)):
                viz_key = f"users/{user_id}/results/{analysis_id}/visualization_{i}.png"
                viz_url = s3.generate_presigned_url(
                    'get_object',
                    Params={'Bucket': S3_BUCKET, 'Key': viz_key},
                    ExpiresIn=3600
                )
                visualizations.append(viz_url)

            return create_response(200, {
                'success': True,
                'data': {
                    'analysisId': analysis_id,
                    'results': results_data,
                    'reportUrl': report_url,
                    'visualizations': visualizations
                },
                'message': 'Results retrieved successfully',
                'statusCode': 200
            })

        except s3.exceptions.NoSuchKey:
            return create_response(404, {
                'success': False,
                'message': 'Results not yet available',
                'statusCode': 404
            })
        except Exception as e:
            print(f"Error retrieving results: {e}")
            return create_response(500, {
                'success': False,
                'message': 'Failed to retrieve results',
                'statusCode': 500
            })

    except Exception as e:
        print(f"Get results error: {str(e)}")
        return create_response(500, {
            'success': False,
            'message': 'Internal server error',
            'statusCode': 500
        })
