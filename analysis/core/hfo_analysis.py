import numpy as np
from scipy.signal import butter, filtfilt, hilbert, find_peaks


def iirnotch_hfo(Wo, BW, Ab=-3):
    Gb = 10 ** (-Ab / 20)
    Gb = np.clip(Gb, 0, 1)

    BW = BW * np.pi
    Wo = Wo * np.pi

    beta = (np.sqrt(np.abs(1 - Gb ** 2)) / Gb) * np.tan(BW / 2)
    gain = 1 / (1 + beta)

    num = gain * np.array([1, -2 * np.cos(Wo), 1])
    den = np.array([1, -2 * gain * np.cos(Wo), 2 * gain - 1])

    return num, den


def run_hfo_detection(eeg_data, sampling_rate, analysis_params):
    try:
        locutoff = analysis_params.get('lowCutoffFilter', 80)
        hicutoff = analysis_params.get('highCutoffFilter', 250)

        threshold_settings = analysis_params.get('thresholdSettings', {})
        thresh = threshold_settings.get('amplitude1', 5)
        thresh2 = threshold_settings.get('amplitude2', 3)
        min_HFO_len = threshold_settings.get('duration', 10)
        num_peaks = threshold_settings.get('peaks1', 6)
        num_peaks2 = threshold_settings.get('peaks2', 3)

        synchronization_settings = analysis_params.get('synchronizationSettings', {})
        # min_break = synchronization_settings.get('temporalSync', 10)  # Unused variable
        con_delay = synchronization_settings.get('spatialSync', 0)

        segment_settings = analysis_params.get('segmentSelectionSettings', {})
        analysis_start = segment_settings.get('startTime', 0)
        analysis_end = segment_settings.get('endTime', -1)

        if analysis_end == -1:
            analysis_end = len(eeg_data[0]) / sampling_rate

        sample_start = int(analysis_start * sampling_rate)
        sample_end = int(analysis_end * sampling_rate)

        if len(eeg_data.shape) == 1:
            eeg_data = eeg_data.reshape(1, -1)

        num_channels = eeg_data.shape[0]
        data_segment = eeg_data[:, sample_start:sample_end]

        notch_freq = 60
        Q = 30
        Wo = notch_freq / (sampling_rate / 2)
        BW = Wo / Q
        b_notch, a_notch = iirnotch_hfo(Wo, BW)

        nyquist = sampling_rate / 2
        low = locutoff / nyquist
        high = hicutoff / nyquist
        b_bandpass, a_bandpass = butter(3, [low, high], btype='band')

        hfo_results = {
            'channels': [],
            'total_hfos': 0,
            'hfo_rate': 0,
            'analysis_duration': analysis_end - analysis_start
        }

        all_hfo_events = []

        for ch in range(num_channels):
            channel_data = data_segment[ch, :]

            if notch_freq > 0:
                channel_data = filtfilt(b_notch, a_notch, channel_data)

            filtered_data = filtfilt(b_bandpass, a_bandpass, channel_data)

            analytic_signal = hilbert(filtered_data)
            envelope = np.abs(analytic_signal)

            baseline_window = int(2 * sampling_rate)
            baseline = np.median(envelope[:baseline_window])
            std_baseline = np.std(envelope[:baseline_window])

            threshold = baseline + thresh * std_baseline

            above_threshold = envelope > threshold

            changes = np.diff(np.concatenate(([0], above_threshold.astype(int), [0])))
            starts = np.where(changes == 1)[0]
            ends = np.where(changes == -1)[0]

            channel_hfos = []

            for start, end in zip(starts, ends):
                duration_ms = (end - start) / sampling_rate * 1000

                if duration_ms >= min_HFO_len:
                    segment = filtered_data[start:end]
                    peaks, _ = find_peaks(segment)

                    if len(peaks) >= num_peaks:
                        peak_amplitudes = segment[peaks]
                        high_peaks = np.sum(peak_amplitudes > (baseline + thresh2 * std_baseline))

                        if high_peaks >= num_peaks2:
                            hfo_event = {
                                'channel': ch,
                                'start_time': (start + sample_start) / sampling_rate,
                                'end_time': (end + sample_start) / sampling_rate,
                                'duration_ms': duration_ms,
                                'num_peaks': len(peaks),
                                'max_amplitude': np.max(envelope[start:end]),
                                'mean_frequency': estimate_frequency(segment, sampling_rate)
                            }
                            channel_hfos.append(hfo_event)
                            all_hfo_events.append(hfo_event)

            hfo_results['channels'].append({
                'channel_index': ch,
                'hfo_count': len(channel_hfos),
                'hfo_rate': len(channel_hfos) / (analysis_end - analysis_start),
                'events': channel_hfos
            })

        hfo_results['total_hfos'] = len(all_hfo_events)
        hfo_results['hfo_rate'] = len(all_hfo_events) / (analysis_end - analysis_start)
        hfo_results['all_events'] = sorted(all_hfo_events, key=lambda x: x['start_time'])

        if con_delay > 0:
            hfo_results['synchronized_events'] = find_synchronized_hfos(all_hfo_events, con_delay / 1000)

        return hfo_results

    except Exception as e:
        print(f"HFO detection error: {e}")
        raise e


def estimate_frequency(signal, sampling_rate):
    try:
        fft = np.fft.fft(signal)
        freqs = np.fft.fftfreq(len(signal), 1 / sampling_rate)

        positive_freqs = freqs[:len(freqs) // 2]
        positive_fft = np.abs(fft[:len(fft) // 2])

        peak_idx = np.argmax(positive_fft)
        peak_freq = positive_freqs[peak_idx]

        return peak_freq
    except BaseException:
        return 0


def find_synchronized_hfos(hfo_events, max_delay):
    synchronized_groups = []
    used_events = set()

    for i, event1 in enumerate(hfo_events):
        if i in used_events:
            continue

        group = [event1]
        used_events.add(i)

        for j, event2 in enumerate(hfo_events[i + 1:], i + 1):
            if j in used_events:
                continue

            if event2['channel'] != event1['channel']:
                time_diff = abs(event1['start_time'] - event2['start_time'])

                if time_diff <= max_delay:
                    group.append(event2)
                    used_events.add(j)

        if len(group) > 1:
            synchronized_groups.append({
                'channels': [e['channel'] for e in group],
                'events': group,
                'synchronization_delay': max([abs(e['start_time'] - group[0]['start_time']) for e in group])
            })

    return synchronized_groups


def calculate_hfo_characteristics(hfo_event, raw_data, sampling_rate):
    start_sample = int(hfo_event['start_time'] * sampling_rate)
    end_sample = int(hfo_event['end_time'] * sampling_rate)

    hfo_segment = raw_data[start_sample:end_sample]

    characteristics = {
        'duration_ms': hfo_event['duration_ms'],
        'peak_frequency': hfo_event['mean_frequency'],
        'max_amplitude': hfo_event['max_amplitude'],
        'num_peaks': hfo_event['num_peaks'],
        'energy': np.sum(hfo_segment ** 2),
        'power': np.mean(hfo_segment ** 2)
    }

    return characteristics
